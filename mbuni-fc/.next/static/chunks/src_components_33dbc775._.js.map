{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { Play, Calendar, Trophy, Users } from 'lucide-react'\nimport Container from '@/components/ui/Container'\nimport Button from '@/components/ui/Button'\n\nconst HeroSection: React.FC = () => {\n  const stats = [\n    { icon: Trophy, label: 'Trophies Won', value: '15' },\n    { icon: Users, label: 'Squad Players', value: '28' },\n    { icon: Calendar, label: 'Years Active', value: '25' },\n  ]\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-hero\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-pattern opacity-10\" />\n      \n      {/* Background Image Overlay */}\n      <div className=\"absolute inset-0 bg-black/40\" />\n\n      {/* Floating Elements */}\n      <motion.div\n        className=\"absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full\"\n        animate={{ y: [0, -20, 0] }}\n        transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n      />\n      <motion.div\n        className=\"absolute bottom-32 right-16 w-16 h-16 bg-primary-400/20 rounded-full\"\n        animate={{ y: [0, 20, 0] }}\n        transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\n      />\n      <motion.div\n        className=\"absolute top-1/3 right-20 w-12 h-12 bg-white/5 rounded-full\"\n        animate={{ y: [0, -15, 0] }}\n        transition={{ duration: 5, repeat: Infinity, ease: \"easeInOut\", delay: 2 }}\n      />\n\n      <Container className=\"relative z-10\">\n        <div className=\"text-center text-white\">\n          {/* Main Hero Content */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <motion.h1 \n              className=\"text-5xl md:text-6xl lg:text-7xl font-bold font-display mb-6 leading-tight\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n            >\n              Welcome to{' '}\n              <span className=\"text-gradient bg-gradient-to-r from-white to-primary-200 bg-clip-text text-transparent\">\n                Mbuni FC\n              </span>\n            </motion.h1>\n\n            <motion.p \n              className=\"text-xl md:text-2xl lg:text-3xl text-primary-100 mb-8 max-w-4xl mx-auto leading-relaxed\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n            >\n              Where passion meets excellence. Join us on our journey to greatness \n              as we compete at the highest level of football.\n            </motion.p>\n\n            <motion.div \n              className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n            >\n              <Button \n                variant=\"primary\" \n                size=\"lg\"\n                className=\"bg-white text-primary-600 hover:bg-primary-50 shadow-2xl\"\n              >\n                Get Season Tickets\n              </Button>\n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                className=\"border-white text-white hover:bg-white hover:text-primary-600\"\n                icon={<Play className=\"w-5 h-5\" />}\n              >\n                Watch Highlights\n              </Button>\n            </motion.div>\n          </motion.div>\n\n          {/* Stats Section */}\n          <motion.div \n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n          >\n            {stats.map((stat, index) => {\n              const Icon = stat.icon\n              return (\n                <motion.div\n                  key={stat.label}\n                  className=\"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\"\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}\n                  whileHover={{ scale: 1.05, y: -5 }}\n                >\n                  <Icon className=\"w-8 h-8 text-primary-300 mx-auto mb-4\" />\n                  <div className=\"text-3xl font-bold text-white mb-2\">\n                    {stat.value}\n                  </div>\n                  <div className=\"text-primary-200 text-sm font-medium\">\n                    {stat.label}\n                  </div>\n                </motion.div>\n              )\n            })}\n          </motion.div>\n        </div>\n      </Container>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.2 }}\n      >\n        <motion.div\n          className=\"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n        >\n          <motion.div\n            className=\"w-1 h-3 bg-white rounded-full mt-2\"\n            animate={{ opacity: [1, 0, 1] }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  )\n}\n\nexport default HeroSection\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;AAQA,MAAM,cAAwB;IAC5B,MAAM,QAAQ;QACZ;YAAE,MAAM,mNAAM;YAAE,OAAO;YAAgB,OAAO;QAAK;QACnD;YAAE,MAAM,gNAAK;YAAE,OAAO;YAAiB,OAAO;QAAK;QACnD;YAAE,MAAM,yNAAQ;YAAE,OAAO;YAAgB,OAAO;QAAK;KACtD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;gBAAC;gBAC1B,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;;;;;;0BAEjE,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;gBAAC;gBACzB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;oBAAa,OAAO;gBAAE;;;;;;0BAE3E,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;gBAAC;gBAC1B,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;oBAAa,OAAO;gBAAE;;;;;;0BAG3E,6LAAC,mJAAS;gBAAC,WAAU;0BACnB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC,uMAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;wCACzC;wCACY;sDACX,6LAAC;4CAAK,WAAU;sDAAyF;;;;;;;;;;;;8CAK3G,6LAAC,uMAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAKD,6LAAC,uMAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,6LAAC,gJAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,gJAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,oBAAM,6LAAC,6MAAI;gDAAC,WAAU;;;;;;sDACvB;;;;;;;;;;;;;;;;;;sCAOL,6LAAC,uMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAEvC,MAAM,GAAG,CAAC,CAAC,MAAM;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,6LAAC,uMAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;;sDAEjC,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAZR,KAAK,KAAK;;;;;4BAgBrB;;;;;;;;;;;;;;;;;0BAMN,6LAAC,uMAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;0BAEtC,cAAA,6LAAC,uMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAY;8BAE/D,cAAA,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;gCAAC;gCAAG;gCAAG;6BAAE;wBAAC;wBAC9B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAY;;;;;;;;;;;;;;;;;;;;;;AAM3E;KA3IM;uCA6IS", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/ui/Card.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface CardProps {\n  children: React.ReactNode\n  className?: string\n  hover?: boolean\n  glow?: boolean\n  padding?: 'none' | 'sm' | 'md' | 'lg'\n  onClick?: () => void\n}\n\nconst Card: React.FC<CardProps> = ({ \n  children, \n  className, \n  hover = false, \n  glow = false,\n  padding = 'md',\n  onClick \n}) => {\n  const paddingClasses = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  }\n\n  const cardClasses = cn(\n    'card',\n    hover && 'card-hover',\n    glow && 'card-glow',\n    paddingClasses[padding],\n    onClick && 'cursor-pointer',\n    className\n  )\n\n  if (onClick) {\n    return (\n      <motion.div\n        className={cardClasses}\n        onClick={onClick}\n        whileHover={{ y: -4 }}\n        whileTap={{ scale: 0.98 }}\n        transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n      >\n        {children}\n      </motion.div>\n    )\n  }\n\n  return (\n    <motion.div\n      className={cardClasses}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\nexport default Card\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAeA,MAAM,OAA4B;QAAC,EACjC,QAAQ,EACR,SAAS,EACT,QAAQ,KAAK,EACb,OAAO,KAAK,EACZ,UAAU,IAAI,EACd,OAAO,EACR;IACC,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,IAAA,4HAAE,EACpB,QACA,SAAS,cACT,QAAQ,aACR,cAAc,CAAC,QAAQ,EACvB,WAAW,kBACX;IAGF,IAAI,SAAS;QACX,qBACE,6LAAC,uMAAM,CAAC,GAAG;YACT,WAAW;YACX,SAAS;YACT,YAAY;gBAAE,GAAG,CAAC;YAAE;YACpB,UAAU;gBAAE,OAAO;YAAK;YACxB,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;sBAEzD;;;;;;IAGP;IAEA,qBACE,6LAAC,uMAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE3B;;;;;;AAGP;KAhDM;uCAkDS", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { motion } from 'framer-motion'\nimport { Calendar, Clock, ArrowRight, User } from 'lucide-react'\nimport Container from '@/components/ui/Container'\nimport Card from '@/components/ui/Card'\nimport <PERSON><PERSON> from '@/components/ui/Button'\nimport { formatDate, truncateText } from '@/lib/utils'\n\nconst LatestNews: React.FC = () => {\n  // Mock news data - in a real app, this would come from an API\n  const newsArticles = [\n    {\n      id: 1,\n      title: \"Mbuni FC Secures Victory in Championship Final\",\n      excerpt: \"In a thrilling match that went into extra time, Mbuni FC emerged victorious with a 2-1 win against their rivals, securing their place in football history.\",\n      image: \"/images/news/championship-win.jpg\",\n      author: \"Sports Reporter\",\n      publishedAt: new Date('2024-01-15'),\n      category: \"Match Report\",\n      featured: true\n    },\n    {\n      id: 2,\n      title: \"New Signing: Star Midfielder Joins <PERSON>\",\n      excerpt: \"We're excited to announce the signing of international midfielder <PERSON>, who brings years of experience from top European leagues.\",\n      image: \"/images/news/new-signing.jpg\",\n      author: \"Club Official\",\n      publishedAt: new Date('2024-01-12'),\n      category: \"Transfer News\",\n      featured: false\n    },\n    {\n      id: 3,\n      title: \"Youth Academy Produces Another Talent\",\n      excerpt: \"17-year-old striker Marcus Thompson has been promoted to the first team after impressive performances in the youth league.\",\n      image: \"/images/news/youth-talent.jpg\",\n      author: \"Youth Coach\",\n      publishedAt: new Date('2024-01-10'),\n      category: \"Youth News\",\n      featured: false\n    },\n    {\n      id: 4,\n      title: \"Stadium Renovation Project Begins\",\n      excerpt: \"Construction work has begun on the new north stand, which will increase capacity and improve fan experience at Mbuni Stadium.\",\n      image: \"/images/news/stadium-renovation.jpg\",\n      author: \"Club Management\",\n      publishedAt: new Date('2024-01-08'),\n      category: \"Club News\",\n      featured: false\n    }\n  ]\n\n  const featuredArticle = newsArticles.find(article => article.featured)\n  const regularArticles = newsArticles.filter(article => !article.featured)\n\n  return (\n    <section className=\"section-padding bg-secondary-50\">\n      <Container>\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"heading-2 mb-4\">Latest News</h2>\n          <p className=\"body-large max-w-2xl mx-auto\">\n            Stay updated with the latest happenings at Mbuni FC. From match reports \n            to transfer news and club updates.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Featured Article */}\n          {featuredArticle && (\n            <motion.div\n              className=\"lg:col-span-2\"\n              initial={{ opacity: 0, x: -30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <Card hover className=\"overflow-hidden h-full\">\n                <div className=\"relative h-64 lg:h-80\">\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-10\" />\n                  <div className=\"absolute top-4 left-4 z-20\">\n                    <span className=\"bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                      {featuredArticle.category}\n                    </span>\n                  </div>\n                  <div className=\"absolute bottom-4 left-4 right-4 z-20 text-white\">\n                    <h3 className=\"text-2xl font-bold mb-2 leading-tight\">\n                      {featuredArticle.title}\n                    </h3>\n                    <p className=\"text-gray-200 mb-4 leading-relaxed\">\n                      {truncateText(featuredArticle.excerpt, 120)}\n                    </p>\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-300\">\n                      <div className=\"flex items-center space-x-1\">\n                        <User className=\"w-4 h-4\" />\n                        <span>{featuredArticle.author}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar className=\"w-4 h-4\" />\n                        <span>{formatDate(featuredArticle.publishedAt)}</span>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Placeholder for image */}\n                  <div className=\"w-full h-full bg-gradient-to-br from-primary-400 to-primary-600\" />\n                </div>\n              </Card>\n            </motion.div>\n          )}\n\n          {/* Regular Articles */}\n          <div className=\"space-y-6\">\n            {regularArticles.slice(0, 3).map((article, index) => (\n              <motion.div\n                key={article.id}\n                initial={{ opacity: 0, x: 30 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Card hover className=\"overflow-hidden\">\n                  <div className=\"flex space-x-4\">\n                    <div className=\"relative w-24 h-24 flex-shrink-0\">\n                      {/* Placeholder for image */}\n                      <div className=\"w-full h-full bg-gradient-to-br from-primary-300 to-primary-500 rounded-lg\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"mb-2\">\n                        <span className=\"text-xs font-medium text-primary-600 bg-primary-100 px-2 py-1 rounded\">\n                          {article.category}\n                        </span>\n                      </div>\n                      <h4 className=\"font-semibold text-secondary-900 mb-2 leading-tight\">\n                        {truncateText(article.title, 60)}\n                      </h4>\n                      <p className=\"text-sm text-secondary-600 mb-3 leading-relaxed\">\n                        {truncateText(article.excerpt, 80)}\n                      </p>\n                      <div className=\"flex items-center space-x-3 text-xs text-secondary-500\">\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"w-3 h-3\" />\n                          <span>{formatDate(article.publishedAt)}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* View All News Button */}\n        <motion.div\n          className=\"text-center mt-12\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n          viewport={{ once: true }}\n        >\n          <Button \n            variant=\"outline\" \n            size=\"lg\"\n            icon={<ArrowRight className=\"w-5 h-5\" />}\n            iconPosition=\"right\"\n          >\n            View All News\n          </Button>\n        </motion.div>\n      </Container>\n    </section>\n  )\n}\n\nexport default LatestNews\n"], "names": [], "mappings": ";;;;;AAKA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;AAYA,MAAM,aAAuB;IAC3B,8DAA8D;IAC9D,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa,IAAI,KAAK;YACtB,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa,IAAI,KAAK;YACtB,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa,IAAI,KAAK;YACtB,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YAC<PERSON>,OAAO;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,aAAa,IAAI,KAAK;YACtB,UAAU;YACV,UAAU;QACZ;KACD;IAED,MAAM,kBAAkB,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,QAAQ;IACrE,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,QAAQ;IAExE,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC,mJAAS;;8BACR,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAiB;;;;;;sCAC/B,6LAAC;4BAAE,WAAU;sCAA+B;;;;;;;;;;;;8BAM9C,6LAAC;oBAAI,WAAU;;wBAEZ,iCACC,6LAAC,uMAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,8IAAI;gCAAC,KAAK;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,gBAAgB,QAAQ;;;;;;;;;;;sDAG7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,gBAAgB,KAAK;;;;;;8DAExB,6LAAC;oDAAE,WAAU;8DACV,IAAA,sIAAY,EAAC,gBAAgB,OAAO,EAAE;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;8EAAM,gBAAgB,MAAM;;;;;;;;;;;;sEAE/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yNAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;8EAAM,IAAA,oIAAU,EAAC,gBAAgB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sDAKnD,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAOvB,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC,uMAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;8CAEvB,cAAA,6LAAC,8IAAI;wCAAC,KAAK;wCAAC,WAAU;kDACpB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAEb,cAAA,6LAAC;wDAAI,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,QAAQ,QAAQ;;;;;;;;;;;sEAGrB,6LAAC;4DAAG,WAAU;sEACX,IAAA,sIAAY,EAAC,QAAQ,KAAK,EAAE;;;;;;sEAE/B,6LAAC;4DAAE,WAAU;sEACV,IAAA,sIAAY,EAAC,QAAQ,OAAO,EAAE;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,yNAAQ;wEAAC,WAAU;;;;;;kFACpB,6LAAC;kFAAM,IAAA,oIAAU,EAAC,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA3B1C,QAAQ,EAAE;;;;;;;;;;;;;;;;8BAuCvB,6LAAC,uMAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC,gJAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,oBAAM,6LAAC,mOAAU;4BAAC,WAAU;;;;;;wBAC5B,cAAa;kCACd;;;;;;;;;;;;;;;;;;;;;;AAOX;KA1KM;uCA4KS", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { Calendar, Clock, MapPin, Ticket, ArrowRight } from 'lucide-react'\nimport Container from '@/components/ui/Container'\nimport Card from '@/components/ui/Card'\nimport Button from '@/components/ui/Button'\nimport { formatDate, formatTime } from '@/lib/utils'\n\nconst UpcomingMatches: React.FC = () => {\n  // Mock match data - in a real app, this would come from an API\n  const upcomingMatches = [\n    {\n      id: 1,\n      homeTeam: {\n        name: \"Mbuni FC\",\n        logo: \"/images/teams/mbuni-fc.png\",\n        isHome: true\n      },\n      awayTeam: {\n        name: \"Thunder United\",\n        logo: \"/images/teams/thunder-united.png\",\n        isHome: false\n      },\n      date: new Date('2024-01-20T15:00:00'),\n      venue: \"Mbuni Stadium\",\n      competition: \"Premier League\",\n      ticketsAvailable: true,\n      featured: true\n    },\n    {\n      id: 2,\n      homeTeam: {\n        name: \"City Rovers\",\n        logo: \"/images/teams/city-rovers.png\",\n        isHome: false\n      },\n      awayTeam: {\n        name: \"Mbuni FC\",\n        logo: \"/images/teams/mbuni-fc.png\",\n        isHome: true\n      },\n      date: new Date('2024-01-27T14:30:00'),\n      venue: \"City Stadium\",\n      competition: \"Premier League\",\n      ticketsAvailable: true,\n      featured: false\n    },\n    {\n      id: 3,\n      homeTeam: {\n        name: \"Mbuni FC\",\n        logo: \"/images/teams/mbuni-fc.png\",\n        isHome: true\n      },\n      awayTeam: {\n        name: \"Eagles FC\",\n        logo: \"/images/teams/eagles-fc.png\",\n        isHome: false\n      },\n      date: new Date('2024-02-03T16:00:00'),\n      venue: \"Mbuni Stadium\",\n      competition: \"FA Cup\",\n      ticketsAvailable: true,\n      featured: false\n    }\n  ]\n\n  const featuredMatch = upcomingMatches.find(match => match.featured)\n  const otherMatches = upcomingMatches.filter(match => !match.featured)\n\n  return (\n    <section className=\"section-padding bg-white\">\n      <Container>\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"heading-2 mb-4\">Upcoming Matches</h2>\n          <p className=\"body-large max-w-2xl mx-auto\">\n            Don't miss our upcoming fixtures. Get your tickets now and support \n            the team at home and away.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Featured Match */}\n          {featuredMatch && (\n            <motion.div\n              initial={{ opacity: 0, x: -30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n            >\n              <Card glow className=\"relative overflow-hidden\">\n                <div className=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-primary-700\" />\n                \n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <span className=\"bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm font-medium\">\n                      {featuredMatch.competition}\n                    </span>\n                    <span className=\"text-sm text-secondary-500\">Next Match</span>\n                  </div>\n\n                  {/* Teams */}\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <div className=\"text-center flex-1\">\n                      <div className=\"w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-3\">\n                        <span className=\"text-white font-bold text-xl\">M</span>\n                      </div>\n                      <h3 className=\"font-semibold text-secondary-900\">\n                        {featuredMatch.homeTeam.name}\n                      </h3>\n                      <span className=\"text-xs text-secondary-500\">HOME</span>\n                    </div>\n\n                    <div className=\"px-6\">\n                      <div className=\"text-2xl font-bold text-secondary-400\">VS</div>\n                    </div>\n\n                    <div className=\"text-center flex-1\">\n                      <div className=\"w-16 h-16 bg-secondary-300 rounded-full flex items-center justify-center mx-auto mb-3\">\n                        <span className=\"text-white font-bold text-xl\">T</span>\n                      </div>\n                      <h3 className=\"font-semibold text-secondary-900\">\n                        {featuredMatch.awayTeam.name}\n                      </h3>\n                      <span className=\"text-xs text-secondary-500\">AWAY</span>\n                    </div>\n                  </div>\n\n                  {/* Match Details */}\n                  <div className=\"space-y-3 mb-6\">\n                    <div className=\"flex items-center space-x-3 text-secondary-600\">\n                      <Calendar className=\"w-5 h-5 text-primary-500\" />\n                      <span>{formatDate(featuredMatch.date)}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-3 text-secondary-600\">\n                      <Clock className=\"w-5 h-5 text-primary-500\" />\n                      <span>{formatTime(featuredMatch.date)}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-3 text-secondary-600\">\n                      <MapPin className=\"w-5 h-5 text-primary-500\" />\n                      <span>{featuredMatch.venue}</span>\n                    </div>\n                  </div>\n\n                  {/* Ticket Button */}\n                  {featuredMatch.ticketsAvailable && (\n                    <Button \n                      variant=\"primary\" \n                      size=\"lg\" \n                      className=\"w-full\"\n                      icon={<Ticket className=\"w-5 h-5\" />}\n                    >\n                      Get Tickets\n                    </Button>\n                  )}\n                </div>\n              </Card>\n            </motion.div>\n          )}\n\n          {/* Other Matches */}\n          <div className=\"space-y-4\">\n            {otherMatches.map((match, index) => (\n              <motion.div\n                key={match.id}\n                initial={{ opacity: 0, x: 30 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Card hover className=\"p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4 flex-1\">\n                      {/* Teams */}\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center\">\n                          <span className=\"text-white font-bold text-sm\">\n                            {match.homeTeam.name === \"Mbuni FC\" ? \"M\" : match.homeTeam.name.charAt(0)}\n                          </span>\n                        </div>\n                        <span className=\"font-medium text-secondary-900\">\n                          {match.homeTeam.name}\n                        </span>\n                      </div>\n\n                      <span className=\"text-secondary-400 font-medium\">vs</span>\n\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-10 h-10 bg-secondary-300 rounded-full flex items-center justify-center\">\n                          <span className=\"text-white font-bold text-sm\">\n                            {match.awayTeam.name === \"Mbuni FC\" ? \"M\" : match.awayTeam.name.charAt(0)}\n                          </span>\n                        </div>\n                        <span className=\"font-medium text-secondary-900\">\n                          {match.awayTeam.name}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Match Info */}\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-medium text-secondary-900\">\n                        {formatDate(match.date)}\n                      </div>\n                      <div className=\"text-sm text-secondary-500\">\n                        {formatTime(match.date)}\n                      </div>\n                      <div className=\"text-xs text-primary-600 font-medium mt-1\">\n                        {match.competition}\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              </motion.div>\n            ))}\n\n            {/* View All Fixtures Button */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n              className=\"pt-4\"\n            >\n              <Button \n                variant=\"outline\" \n                size=\"md\"\n                className=\"w-full\"\n                icon={<ArrowRight className=\"w-4 h-4\" />}\n                iconPosition=\"right\"\n              >\n                View All Fixtures\n              </Button>\n            </motion.div>\n          </div>\n        </div>\n      </Container>\n    </section>\n  )\n}\n\nexport default UpcomingMatches\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA,MAAM,kBAA4B;IAChC,+DAA+D;IAC/D,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA,MAAM,IAAI,KAAK;YACf,OAAO;YACP,aAAa;YACb,kBAAkB;YAClB,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA,MAAM,IAAI,KAAK;YAC<PERSON>,OAAO;YACP,aAAa;YACb,kBAAkB;YAClB,UAAU;QACZ;QACA;YACE,IAAI;YACJ,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA,UAAU;gBACR,MAAM;gBACN,MAAM;gBACN,QAAQ;YACV;YACA,MAAM,IAAI,KAAK;YACf,OAAO;YACP,aAAa;YACb,kBAAkB;YAClB,UAAU;QACZ;KACD;IAED,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ;IAClE,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,QAAQ;IAEpE,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC,mJAAS;;8BACR,6LAAC,uMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAiB;;;;;;sCAC/B,6LAAC;4BAAE,WAAU;sCAA+B;;;;;;;;;;;;8BAM9C,6LAAC;oBAAI,WAAU;;wBAEZ,+BACC,6LAAC,uMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,8IAAI;gCAAC,IAAI;gCAAC,WAAU;;kDACnB,6LAAC;wCAAI,WAAU;;;;;;kDAEf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,cAAc,WAAW;;;;;;kEAE5B,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAI/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;;;;;;0EAEjD,6LAAC;gEAAG,WAAU;0EACX,cAAc,QAAQ,CAAC,IAAI;;;;;;0EAE9B,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;;kEAG/C,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;kEAGzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;;;;;;0EAEjD,6LAAC;gEAAG,WAAU;0EACX,cAAc,QAAQ,CAAC,IAAI;;;;;;0EAE9B,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;;;;;;;;0DAKjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yNAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;0EAAM,IAAA,oIAAU,EAAC,cAAc,IAAI;;;;;;;;;;;;kEAEtC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,gNAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;0EAAM,IAAA,oIAAU,EAAC,cAAc,IAAI;;;;;;;;;;;;kEAEtC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uNAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;0EAAM,cAAc,KAAK;;;;;;;;;;;;;;;;;;4CAK7B,cAAc,gBAAgB,kBAC7B,6LAAC,gJAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,oBAAM,6LAAC,mNAAM;oDAAC,WAAU;;;;;;0DACzB;;;;;;;;;;;;;;;;;;;;;;;sCAUX,6LAAC;4BAAI,WAAU;;gCACZ,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC,uMAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;kDAEvB,cAAA,6LAAC,8IAAI;4CAAC,KAAK;4CAAC,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFACb,MAAM,QAAQ,CAAC,IAAI,KAAK,aAAa,MAAM,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kFAG3E,6LAAC;wEAAK,WAAU;kFACb,MAAM,QAAQ,CAAC,IAAI;;;;;;;;;;;;0EAIxB,6LAAC;gEAAK,WAAU;0EAAiC;;;;;;0EAEjD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFACb,MAAM,QAAQ,CAAC,IAAI,KAAK,aAAa,MAAM,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kFAG3E,6LAAC;wEAAK,WAAU;kFACb,MAAM,QAAQ,CAAC,IAAI;;;;;;;;;;;;;;;;;;kEAM1B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,IAAA,oIAAU,EAAC,MAAM,IAAI;;;;;;0EAExB,6LAAC;gEAAI,WAAU;0EACZ,IAAA,oIAAU,EAAC,MAAM,IAAI;;;;;;0EAExB,6LAAC;gEAAI,WAAU;0EACZ,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;uCA5CrB,MAAM,EAAE;;;;;8CAqDjB,6LAAC,uMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,6LAAC,gJAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,oBAAM,6LAAC,mOAAU;4CAAC,WAAU;;;;;;wCAC5B,cAAa;kDACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KA7OM;uCA+OS", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { Star, Target, Shield, Zap, ArrowRight } from 'lucide-react'\nimport Container from '@/components/ui/Container'\nimport Card from '@/components/ui/Card'\nimport <PERSON><PERSON> from '@/components/ui/Button'\n\nconst PlayerHighlights: React.FC = () => {\n  // Mock player data - in a real app, this would come from an API\n  const featuredPlayers = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      position: \"Forward\",\n      number: 10,\n      image: \"/images/players/marcus-rodriguez.jpg\",\n      stats: {\n        goals: 18,\n        assists: 12,\n        matches: 25\n      },\n      achievements: [\"Top Scorer\", \"Player of the Month\"],\n      description: \"Clinical finisher with exceptional pace and positioning.\",\n      icon: Target\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      position: \"Midfielder\",\n      number: 8,\n      image: \"/images/players/david-thompson.jpg\",\n      stats: {\n        goals: 6,\n        assists: 15,\n        matches: 28\n      },\n      achievements: [\"Most Assists\", \"Captain\"],\n      description: \"Creative playmaker who controls the tempo of the game.\",\n      icon: <PERSON>\n    },\n    {\n      id: 3,\n      name: \"<PERSON>\",\n      position: \"Defender\",\n      number: 4,\n      image: \"/images/players/alex-johnson.jpg\",\n      stats: {\n        goals: 3,\n        assists: 4,\n        matches: 30\n      },\n      achievements: [\"Most Clean Sheets\", \"Defensive Rock\"],\n      description: \"Solid defender with excellent aerial ability and leadership.\",\n      icon: Shield\n    },\n    {\n      id: 4,\n      name: \"Ryan Mitchell\",\n      position: \"Winger\",\n      number: 7,\n      image: \"/images/players/ryan-mitchell.jpg\",\n      stats: {\n        goals: 12,\n        assists: 8,\n        matches: 24\n      },\n      achievements: [\"Fastest Player\", \"Breakthrough Player\"],\n      description: \"Explosive winger with incredible dribbling skills.\",\n      icon: Zap\n    }\n  ]\n\n  return (\n    <section className=\"section-padding bg-secondary-900 text-white relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-pattern opacity-5\" />\n      \n      <Container className=\"relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"heading-2 text-white mb-4\">Player Highlights</h2>\n          <p className=\"body-large text-secondary-300 max-w-2xl mx-auto\">\n            Meet our star players who are making the difference on the pitch. \n            Their skill, dedication, and passion drive our success.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n          {featuredPlayers.map((player, index) => {\n            const Icon = player.icon\n            return (\n              <motion.div\n                key={player.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <Card className=\"bg-secondary-800 border-secondary-700 hover:bg-secondary-750 transition-all duration-300 group\">\n                  <div className=\"relative\">\n                    {/* Player Image Placeholder */}\n                    <div className=\"relative h-48 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg mb-4 overflow-hidden\">\n                      <div className=\"absolute inset-0 bg-black/20\" />\n                      <div className=\"absolute bottom-4 left-4\">\n                        <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center\">\n                          <span className=\"text-primary-600 font-bold text-sm\">\n                            {player.number}\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"absolute top-4 right-4\">\n                        <Icon className=\"w-6 h-6 text-white/80\" />\n                      </div>\n                    </div>\n\n                    {/* Player Info */}\n                    <div className=\"space-y-3\">\n                      <div>\n                        <h3 className=\"text-lg font-bold text-white group-hover:text-primary-300 transition-colors\">\n                          {player.name}\n                        </h3>\n                        <p className=\"text-primary-400 text-sm font-medium\">\n                          {player.position}\n                        </p>\n                      </div>\n\n                      <p className=\"text-secondary-300 text-sm leading-relaxed\">\n                        {player.description}\n                      </p>\n\n                      {/* Stats */}\n                      <div className=\"grid grid-cols-3 gap-2 py-3 border-t border-secondary-700\">\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-white\">\n                            {player.stats.goals}\n                          </div>\n                          <div className=\"text-xs text-secondary-400\">Goals</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-white\">\n                            {player.stats.assists}\n                          </div>\n                          <div className=\"text-xs text-secondary-400\">Assists</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-lg font-bold text-white\">\n                            {player.stats.matches}\n                          </div>\n                          <div className=\"text-xs text-secondary-400\">Matches</div>\n                        </div>\n                      </div>\n\n                      {/* Achievements */}\n                      <div className=\"space-y-1\">\n                        {player.achievements.slice(0, 2).map((achievement, idx) => (\n                          <div\n                            key={idx}\n                            className=\"text-xs bg-primary-600/20 text-primary-300 px-2 py-1 rounded-full inline-block mr-1\"\n                          >\n                            {achievement}\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                </Card>\n              </motion.div>\n            )\n          })}\n        </div>\n\n        {/* Team Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"bg-secondary-800 rounded-2xl p-8 mb-8\"\n        >\n          <h3 className=\"text-2xl font-bold text-white mb-6 text-center\">\n            Season Statistics\n          </h3>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-primary-400 mb-2\">68</div>\n              <div className=\"text-secondary-300 text-sm\">Goals Scored</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-primary-400 mb-2\">23</div>\n              <div className=\"text-secondary-300 text-sm\">Clean Sheets</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-primary-400 mb-2\">78%</div>\n              <div className=\"text-secondary-300 text-sm\">Win Rate</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-primary-400 mb-2\">2nd</div>\n              <div className=\"text-secondary-300 text-sm\">League Position</div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* View Full Squad Button */}\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.5 }}\n          viewport={{ once: true }}\n        >\n          <Button \n            variant=\"outline\" \n            size=\"lg\"\n            className=\"border-white text-white hover:bg-white hover:text-secondary-900\"\n            icon={<ArrowRight className=\"w-5 h-5\" />}\n            iconPosition=\"right\"\n          >\n            View Full Squad\n          </Button>\n        </motion.div>\n      </Container>\n    </section>\n  )\n}\n\nexport default PlayerHighlights\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AASA,MAAM,mBAA6B;IACjC,gEAAgE;IAChE,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;YACA,cAAc;gBAAC;gBAAc;aAAsB;YACnD,aAAa;YACb,MAAM,mNAAM;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;YACA,cAAc;gBAAC;gBAAgB;aAAU;YACzC,aAAa;YACb,MAAM,6MAAI;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;YACA,cAAc;gBAAC;gBAAqB;aAAiB;YACrD,aAAa;YACb,MAAM,mNAAM;QACd;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;gBACL,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;YACA,cAAc;gBAAC;gBAAkB;aAAsB;YACvD,aAAa;YACb,MAAM,0MAAG;QACX;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC,mJAAS;gBAAC,WAAU;;kCACnB,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAMjE,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ;4BAC5B,MAAM,OAAO,OAAO,IAAI;4BACxB,qBACE,6LAAC,uMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;0CAEvB,cAAA,6LAAC,8IAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,OAAO,MAAM;;;;;;;;;;;;;;;;kEAIpB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;;;;;;;;;;;;;;;;0DAKpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,OAAO,IAAI;;;;;;0EAEd,6LAAC;gEAAE,WAAU;0EACV,OAAO,QAAQ;;;;;;;;;;;;kEAIpB,6LAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;kEAIrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,OAAO,KAAK,CAAC,KAAK;;;;;;kFAErB,6LAAC;wEAAI,WAAU;kFAA6B;;;;;;;;;;;;0EAE9C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,OAAO,KAAK,CAAC,OAAO;;;;;;kFAEvB,6LAAC;wEAAI,WAAU;kFAA6B;;;;;;;;;;;;0EAE9C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,OAAO,KAAK,CAAC,OAAO;;;;;;kFAEvB,6LAAC;wEAAI,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;kEAKhD,6LAAC;wDAAI,WAAU;kEACZ,OAAO,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,aAAa,oBACjD,6LAAC;gEAEC,WAAU;0EAET;+DAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAhEZ,OAAO,EAAE;;;;;wBA4EpB;;;;;;kCAIF,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAI/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAC1D,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAMlD,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,6LAAC,gJAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,oBAAM,6LAAC,mOAAU;gCAAC,WAAU;;;;;;4BAC5B,cAAa;sCACd;;;;;;;;;;;;;;;;;;;;;;;AAOX;KA9NM;uCAgOS", "debugId": null}}]}