{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function formatTime(date: Date | string): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\n// Animation utilities\nexport const fadeInUp = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 },\n  transition: { duration: 0.5 }\n}\n\nexport const fadeInDown = {\n  initial: { opacity: 0, y: -20 },\n  animate: { opacity: 1, y: 0 },\n  transition: { duration: 0.5 }\n}\n\nexport const slideInLeft = {\n  initial: { opacity: 0, x: -20 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.5 }\n}\n\nexport const slideInRight = {\n  initial: { opacity: 0, x: 20 },\n  animate: { opacity: 1, x: 0 },\n  transition: { duration: 0.5 }\n}\n\nexport const scaleIn = {\n  initial: { opacity: 0, scale: 0.8 },\n  animate: { opacity: 1, scale: 1 },\n  transition: { duration: 0.5 }\n}\n\nexport const staggerContainer = {\n  animate: {\n    transition: {\n      staggerChildren: 0.1\n    }\n  }\n}\n\nexport const staggerItem = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAGO,MAAM,WAAW;IACtB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,aAAa;IACxB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,eAAe;IAC1B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,mBAAmB;IAC9B,SAAS;QACP,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;AAC9B", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/ui/Container.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ContainerProps {\n  children: React.ReactNode\n  className?: string\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'\n  padding?: boolean\n}\n\nconst Container: React.FC<ContainerProps> = ({ \n  children, \n  className, \n  size = 'lg',\n  padding = true \n}) => {\n  const sizeClasses = {\n    sm: 'max-w-3xl',\n    md: 'max-w-5xl',\n    lg: 'max-w-7xl',\n    xl: 'max-w-8xl',\n    full: 'max-w-full'\n  }\n\n  return (\n    <div className={cn(\n      'mx-auto',\n      sizeClasses[size],\n      padding && 'px-4 sm:px-6 lg:px-8',\n      className\n    )}>\n      {children}\n    </div>\n  )\n}\n\nexport default Container\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAYA,MAAM,YAAsC;QAAC,EAC3C,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,IAAI,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAChB,WACA,WAAW,CAAC,KAAK,EACjB,WAAW,wBACX;kBAEC;;;;;;AAGP;KAxBM;uCA0BS", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/ui/Button.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  children: React.ReactNode\n  loading?: boolean\n  icon?: React.ReactNode\n  iconPosition?: 'left' | 'right'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    children, \n    loading = false,\n    icon,\n    iconPosition = 'left',\n    disabled,\n    ...props \n  }, ref) => {\n    const baseClasses = 'btn'\n    const variantClasses = {\n      primary: 'btn-primary',\n      secondary: 'btn-secondary',\n      outline: 'btn-outline',\n      ghost: 'btn-ghost'\n    }\n    const sizeClasses = {\n      sm: 'btn-sm',\n      md: 'btn-md',\n      lg: 'btn-lg',\n      xl: 'btn-xl'\n    }\n\n    return (\n      <motion.button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          loading && 'opacity-70 cursor-not-allowed',\n          className\n        )}\n        disabled={disabled || loading}\n        whileHover={{ scale: 1.02 }}\n        whileTap={{ scale: 0.98 }}\n        transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        \n        {icon && iconPosition === 'left' && !loading && (\n          <span className=\"mr-2\">{icon}</span>\n        )}\n        \n        {children}\n        \n        {icon && iconPosition === 'right' && !loading && (\n          <span className=\"ml-2\">{icon}</span>\n        )}\n      </motion.button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport default Button\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,uBAAS,wKAAK,CAAC,UAAU,MAC7B,QAUG;QAVF,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IACpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;IACT;IACA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,uMAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,IAAA,4HAAE,EACX,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,WAAW,iCACX;QAEF,UAAU,YAAY;QACtB,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;QACzD,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAKP,QAAQ,iBAAiB,UAAU,CAAC,yBACnC,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAGzB;YAEA,QAAQ,iBAAiB,WAAW,CAAC,yBACpC,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAIhC;;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Menu, X, ChevronDown } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport Container from '@/components/ui/Container'\nimport But<PERSON> from '@/components/ui/Button'\n\nconst Header: React.FC = () => {\n  const [isScrolled, setIsScrolled] = useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const navigationItems = [\n    { name: 'Home', href: '/' },\n    { \n      name: 'Team', \n      href: '/team',\n      dropdown: [\n        { name: 'First Team', href: '/team/first-team' },\n        { name: 'Youth Teams', href: '/team/youth' },\n        { name: 'Coaching Staff', href: '/team/staff' },\n      ]\n    },\n    { \n      name: 'Fixtures & Results', \n      href: '/fixtures',\n      dropdown: [\n        { name: 'Fixtures', href: '/fixtures' },\n        { name: 'Results', href: '/results' },\n        { name: 'League Table', href: '/league-table' },\n      ]\n    },\n    { \n      name: 'News', \n      href: '/news',\n      dropdown: [\n        { name: 'Latest News', href: '/news' },\n        { name: 'Match Reports', href: '/news/match-reports' },\n        { name: 'Press Releases', href: '/news/press' },\n      ]\n    },\n    { \n      name: 'Club', \n      href: '/club',\n      dropdown: [\n        { name: 'About Us', href: '/club/about' },\n        { name: 'History', href: '/club/history' },\n        { name: 'Stadium', href: '/club/stadium' },\n        { name: 'Facilities', href: '/club/facilities' },\n      ]\n    },\n    { name: 'Contact', href: '/contact' },\n  ]\n\n  return (\n    <motion.header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled \n          ? 'bg-white/95 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <Container>\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 lg:w-12 lg:h-12 bg-primary-600 rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg lg:text-xl\">M</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl lg:text-2xl font-bold text-secondary-900\">\n                Mbuni FC\n              </h1>\n              <p className=\"text-xs lg:text-sm text-secondary-600 -mt-1\">\n                Football Club\n              </p>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navigationItems.map((item) => (\n              <div\n                key={item.name}\n                className=\"relative\"\n                onMouseEnter={() => item.dropdown && setActiveDropdown(item.name)}\n                onMouseLeave={() => setActiveDropdown(null)}\n              >\n                <Link\n                  href={item.href}\n                  className={cn(\n                    'flex items-center space-x-1 px-3 py-2 rounded-lg transition-colors duration-200',\n                    'text-secondary-700 hover:text-primary-600 hover:bg-primary-50',\n                    isScrolled ? 'text-secondary-700' : 'text-white hover:text-primary-200 hover:bg-white/10'\n                  )}\n                >\n                  <span>{item.name}</span>\n                  {item.dropdown && (\n                    <ChevronDown className=\"w-4 h-4 transition-transform duration-200\" />\n                  )}\n                </Link>\n\n                {/* Dropdown Menu */}\n                <AnimatePresence>\n                  {item.dropdown && activeDropdown === item.name && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: 10 }}\n                      transition={{ duration: 0.2 }}\n                      className=\"absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-secondary-200 py-2\"\n                    >\n                      {item.dropdown.map((dropdownItem) => (\n                        <Link\n                          key={dropdownItem.name}\n                          href={dropdownItem.href}\n                          className=\"block px-4 py-2 text-secondary-700 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-200\"\n                        >\n                          {dropdownItem.name}\n                        </Link>\n                      ))}\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:block\">\n            <Button variant=\"primary\" size=\"md\">\n              Get Tickets\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"lg:hidden p-2 rounded-lg transition-colors duration-200 hover:bg-secondary-100\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            {isMobileMenuOpen ? (\n              <X className=\"w-6 h-6 text-secondary-700\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-secondary-700\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"lg:hidden bg-white border-t border-secondary-200 py-4\"\n            >\n              <nav className=\"space-y-2\">\n                {navigationItems.map((item) => (\n                  <div key={item.name}>\n                    <Link\n                      href={item.href}\n                      className=\"block px-4 py-2 text-secondary-700 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200\"\n                      onClick={() => setIsMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                    {item.dropdown && (\n                      <div className=\"ml-4 space-y-1\">\n                        {item.dropdown.map((dropdownItem) => (\n                          <Link\n                            key={dropdownItem.name}\n                            href={dropdownItem.href}\n                            className=\"block px-4 py-2 text-sm text-secondary-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200\"\n                            onClick={() => setIsMobileMenuOpen(false)}\n                          >\n                            {dropdownItem.name}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ))}\n                <div className=\"pt-4 border-t border-secondary-200\">\n                  <Button variant=\"primary\" size=\"md\" className=\"w-full\">\n                    Get Tickets\n                  </Button>\n                </div>\n              </nav>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </Container>\n    </motion.header>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUA,MAAM,SAAmB;;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAC;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAgB;IAEpE,IAAA,0KAAS;4BAAC;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YACE,MAAM;YACN,MAAM;YACN,UAAU;gBACR;oBAAE,MAAM;oBAAc,MAAM;gBAAmB;gBAC/C;oBAAE,MAAM;oBAAe,MAAM;gBAAc;gBAC3C;oBAAE,MAAM;oBAAkB,MAAM;gBAAc;aAC/C;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;gBACR;oBAAE,MAAM;oBAAY,MAAM;gBAAY;gBACtC;oBAAE,MAAM;oBAAW,MAAM;gBAAW;gBACpC;oBAAE,MAAM;oBAAgB,MAAM;gBAAgB;aAC/C;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;gBACR;oBAAE,MAAM;oBAAe,MAAM;gBAAQ;gBACrC;oBAAE,MAAM;oBAAiB,MAAM;gBAAsB;gBACrD;oBAAE,MAAM;oBAAkB,MAAM;gBAAc;aAC/C;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;gBACR;oBAAE,MAAM;oBAAY,MAAM;gBAAc;gBACxC;oBAAE,MAAM;oBAAW,MAAM;gBAAgB;gBACzC;oBAAE,MAAM;oBAAW,MAAM;gBAAgB;gBACzC;oBAAE,MAAM;oBAAc,MAAM;gBAAmB;aAChD;QACH;QACA;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC,uMAAM,CAAC,MAAM;QACZ,WAAW,IAAA,4HAAE,EACX,+DACA,aACI,2CACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC,mJAAS;;8BACR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,0KAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA0C;;;;;;;;;;;8CAE5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;sDAGjE,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;;;;;;;sCAO/D,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;oCAEC,WAAU;oCACV,cAAc,IAAM,KAAK,QAAQ,IAAI,kBAAkB,KAAK,IAAI;oCAChE,cAAc,IAAM,kBAAkB;;sDAEtC,6LAAC,0KAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,IAAA,4HAAE,EACX,mFACA,iEACA,aAAa,uBAAuB;;8DAGtC,6LAAC;8DAAM,KAAK,IAAI;;;;;;gDACf,KAAK,QAAQ,kBACZ,6LAAC,sOAAW;oDAAC,WAAU;;;;;;;;;;;;sDAK3B,6LAAC,+MAAe;sDACb,KAAK,QAAQ,IAAI,mBAAmB,KAAK,IAAI,kBAC5C,6LAAC,uMAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC1B,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;0DAET,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,6LAAC,0KAAI;wDAEH,MAAM,aAAa,IAAI;wDACvB,WAAU;kEAET,aAAa,IAAI;uDAJb,aAAa,IAAI;;;;;;;;;;;;;;;;mCA/B3B,KAAK,IAAI;;;;;;;;;;sCA8CpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gJAAM;gCAAC,SAAQ;gCAAU,MAAK;0CAAK;;;;;;;;;;;sCAMtC,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,oBAAoB,CAAC;sCAEnC,iCACC,6LAAC,oMAAC;gCAAC,WAAU;;;;;yFAEb,6LAAC,6MAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMtB,6LAAC,+MAAe;8BACb,kCACC,6LAAC,uMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;;0DACC,6LAAC,0KAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;gDACV,SAAS,IAAM,oBAAoB;0DAElC,KAAK,IAAI;;;;;;4CAEX,KAAK,QAAQ,kBACZ,6LAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,6LAAC,0KAAI;wDAEH,MAAM,aAAa,IAAI;wDACvB,WAAU;wDACV,SAAS,IAAM,oBAAoB;kEAElC,aAAa,IAAI;uDALb,aAAa,IAAI;;;;;;;;;;;uCAZtB,KAAK,IAAI;;;;;8CAwBrB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gJAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzE;GA1MM;KAAA;uCA4MS", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Development/REAL_PROJECTS/mbuni_fc_new/mbuni-fc/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { motion } from 'framer-motion'\nimport { \n  Facebook, \n  Twitter, \n  Instagram, \n  Youtube, \n  Mail, \n  Phone, \n  MapPin,\n  Heart\n} from 'lucide-react'\nimport Container from '@/components/ui/Container'\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear()\n\n  const footerLinks = {\n    club: [\n      { name: 'About Us', href: '/club/about' },\n      { name: 'History', href: '/club/history' },\n      { name: 'Stadium', href: '/club/stadium' },\n      { name: 'Facilities', href: '/club/facilities' },\n      { name: 'Contact', href: '/contact' },\n    ],\n    team: [\n      { name: 'First Team', href: '/team/first-team' },\n      { name: 'Youth Teams', href: '/team/youth' },\n      { name: 'Coaching Staff', href: '/team/staff' },\n      { name: 'Player Profiles', href: '/team/players' },\n    ],\n    matches: [\n      { name: 'Fixtures', href: '/fixtures' },\n      { name: 'Results', href: '/results' },\n      { name: 'League Table', href: '/league-table' },\n      { name: 'Match Reports', href: '/news/match-reports' },\n    ],\n    media: [\n      { name: 'Latest News', href: '/news' },\n      { name: 'Press Releases', href: '/news/press' },\n      { name: 'Photo Gallery', href: '/media/photos' },\n      { name: 'Videos', href: '/media/videos' },\n    ]\n  }\n\n  const socialLinks = [\n    { name: 'Facebook', icon: Facebook, href: '#', color: 'hover:text-blue-600' },\n    { name: 'Twitter', icon: Twitter, href: '#', color: 'hover:text-blue-400' },\n    { name: 'Instagram', icon: Instagram, href: '#', color: 'hover:text-pink-600' },\n    { name: 'YouTube', icon: Youtube, href: '#', color: 'hover:text-red-600' },\n  ]\n\n  return (\n    <footer className=\"bg-secondary-900 text-white\">\n      <Container>\n        <div className=\"section-padding\">\n          {/* Main Footer Content */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12\">\n            {/* Club Info */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"flex items-center space-x-3 mb-6\">\n                  <div className=\"w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-bold text-xl\">M</span>\n                  </div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold\">Mbuni FC</h3>\n                    <p className=\"text-secondary-400\">Football Club</p>\n                  </div>\n                </Link>\n                \n                <p className=\"text-secondary-300 mb-6 leading-relaxed\">\n                  Mbuni FC is more than just a football club. We are a community, \n                  a family, and a symbol of excellence in football. Join us on our \n                  journey to greatness.\n                </p>\n\n                {/* Contact Info */}\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <MapPin className=\"w-5 h-5 text-primary-500\" />\n                    <span className=\"text-secondary-300\">\n                      Mbuni Stadium, Football Avenue, City\n                    </span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Phone className=\"w-5 h-5 text-primary-500\" />\n                    <span className=\"text-secondary-300\">+****************</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Mail className=\"w-5 h-5 text-primary-500\" />\n                    <span className=\"text-secondary-300\"><EMAIL></span>\n                  </div>\n                </div>\n\n                {/* Social Links */}\n                <div className=\"flex space-x-4 mt-6\">\n                  {socialLinks.map((social) => {\n                    const Icon = social.icon\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        className={`p-2 bg-secondary-800 rounded-lg transition-colors duration-200 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <Icon className=\"w-5 h-5\" />\n                      </motion.a>\n                    )\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Quick Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold mb-4\">Club</h4>\n              <ul className=\"space-y-2\">\n                {footerLinks.club.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-secondary-300 hover:text-primary-400 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold mb-4\">Team</h4>\n              <ul className=\"space-y-2\">\n                {footerLinks.team.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-secondary-300 hover:text-primary-400 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold mb-4\">Matches</h4>\n              <ul className=\"space-y-2 mb-6\">\n                {footerLinks.matches.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-secondary-300 hover:text-primary-400 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n\n              <h4 className=\"text-lg font-semibold mb-4\">Media</h4>\n              <ul className=\"space-y-2\">\n                {footerLinks.media.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-secondary-300 hover:text-primary-400 transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n\n          {/* Bottom Footer */}\n          <motion.div\n            className=\"border-t border-secondary-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center\"\n            initial={{ opacity: 0 }}\n            whileInView={{ opacity: 1 }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            <p className=\"text-secondary-400 text-sm mb-4 md:mb-0\">\n              © {currentYear} Mbuni FC. All rights reserved.\n            </p>\n            \n            <div className=\"flex items-center space-x-1 text-secondary-400 text-sm\">\n              <span>Made with</span>\n              <Heart className=\"w-4 h-4 text-primary-500 fill-current\" />\n              <span>for football fans</span>\n            </div>\n          </motion.div>\n        </div>\n      </Container>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAfA;;;;;;AAiBA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,MAAM;YACJ;gBAAE,MAAM;gBAAY,MAAM;YAAc;YACxC;gBAAE,MAAM;gBAAW,MAAM;YAAgB;YACzC;gBAAE,MAAM;gBAAW,MAAM;YAAgB;YACzC;gBAAE,MAAM;gBAAc,MAAM;YAAmB;YAC/C;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,MAAM;YACJ;gBAAE,MAAM;gBAAc,MAAM;YAAmB;YAC/C;gBAAE,MAAM;gBAAe,MAAM;YAAc;YAC3C;gBAAE,MAAM;gBAAkB,MAAM;YAAc;YAC9C;gBAAE,MAAM;gBAAmB,MAAM;YAAgB;SAClD;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAY;YACtC;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAgB,MAAM;YAAgB;YAC9C;gBAAE,MAAM;gBAAiB,MAAM;YAAsB;SACtD;QACD,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAkB,MAAM;YAAc;YAC9C;gBAAE,MAAM;gBAAiB,MAAM;YAAgB;YAC/C;gBAAE,MAAM;gBAAU,MAAM;YAAgB;SACzC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAY,MAAM,yNAAQ;YAAE,MAAM;YAAK,OAAO;QAAsB;QAC5E;YAAE,MAAM;YAAW,MAAM,sNAAO;YAAE,MAAM;YAAK,OAAO;QAAsB;QAC1E;YAAE,MAAM;YAAa,MAAM,4NAAS;YAAE,MAAM;YAAK,OAAO;QAAsB;QAC9E;YAAE,MAAM;YAAW,MAAM,sNAAO;YAAE,MAAM;YAAK,OAAO;QAAqB;KAC1E;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC,mJAAS;sBACR,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC,0KAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAqB;;;;;;sEACnC,6LAAC;4DAAE,WAAU;sEAAqB;;;;;;;;;;;;;;;;;;sDAItC,6LAAC;4CAAE,WAAU;sDAA0C;;;;;;sDAOvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;8DAIvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,gNAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;;;;;;;sDAKzC,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC;gDAChB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,6LAAC,uMAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,WAAW,AAAC,kEAA8E,OAAb,OAAO,KAAK;oDACzF,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,6LAAC;wDAAK,WAAU;;;;;;mDANX,OAAO,IAAI;;;;;4CAStB;;;;;;;;;;;;;;;;;0CAMN,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC;0DACC,cAAA,6LAAC,0KAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC;0DACC,cAAA,6LAAC,0KAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC,uMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,0KAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;kDAWtB,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;0DACC,cAAA,6LAAC,0KAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAc1B,6LAAC,uMAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,6LAAC;gCAAE,WAAU;;oCAA0C;oCAClD;oCAAY;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC,gNAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;KA/MM;uCAiNS", "debugId": null}}]}