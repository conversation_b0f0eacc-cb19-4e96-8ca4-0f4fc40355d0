{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/postcss.config.mjs"], "sourcesContent": ["const config = {\n  plugins: [\"@tailwindcss/postcss\"],\n};\n\nexport default config;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,SAAS;IACb,SAAS;QAAC;KAAuB;AACnC;uCAEe"}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/transforms/transforms.ts"], "sourcesContent": ["/**\n * Shared utilities for our 2 transform implementations.\n */\n\nimport type { Ipc } from '../ipc/evaluate'\nimport { relative, isAbsolute, join, sep } from 'path'\nimport { type StructuredError } from '../ipc'\nimport { type StackFrame } from '../compiled/stacktrace-parser'\n\nexport type IpcInfoMessage =\n  | {\n      type: 'dependencies'\n      envVariables?: string[]\n      directories?: Array<[string, string]>\n      filePaths?: string[]\n      buildFilePaths?: string[]\n    }\n  | {\n      type: 'emittedError'\n      severity: 'warning' | 'error'\n      error: StructuredError\n    }\n  | {\n      type: 'log'\n      logs: Array<{\n        time: number\n        logType: string\n        args: any[]\n        trace?: StackFrame[]\n      }>\n    }\n\nexport type IpcRequestMessage = {\n  type: 'resolve'\n  options: any\n  lookupPath: string\n  request: string\n}\n\nexport type TransformIpc = Ipc<IpcInfoMessage, IpcRequestMessage>\n\nconst contextDir = process.cwd()\nexport const toPath = (file: string) => {\n  const relPath = relative(contextDir, file)\n  if (isAbsolute(relPath)) {\n    throw new Error(\n      `Cannot depend on path (${file}) outside of root directory (${contextDir})`\n    )\n  }\n  return sep !== '/' ? relPath.replaceAll(sep, '/') : relPath\n}\nexport const fromPath = (path: string) => {\n  return join(contextDir, sep !== '/' ? path.replaceAll('/', sep) : path)\n}\n\n// Patch process.env to track which env vars are read\nconst originalEnv = process.env\nconst readEnvVars = new Set<string>()\nprocess.env = new Proxy(originalEnv, {\n  get(target, prop) {\n    if (typeof prop === 'string') {\n      // We register the env var as dependency on the\n      // current transform and all future transforms\n      // since the env var might be cached in module scope\n      // and influence them all\n      readEnvVars.add(prop)\n    }\n    return Reflect.get(target, prop)\n  },\n  set(target, prop, value) {\n    return Reflect.set(target, prop, value)\n  },\n})\n\nexport function getReadEnvVariables(): string[] {\n  return Array.from(readEnvVars)\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAGD;;AAoCA,MAAM,aAAa,QAAQ,GAAG;AACvB,MAAM,SAAS,CAAC;IACrB,MAAM,UAAU,IAAA,6GAAQ,EAAC,YAAY;IACrC,IAAI,IAAA,+GAAU,EAAC,UAAU;QACvB,MAAM,IAAI,MACR,CAAC,uBAAuB,EAAE,KAAK,6BAA6B,EAAE,WAAW,CAAC,CAAC;IAE/E;IACA,OAAO,wGAAG,KAAK,MAAM,QAAQ,UAAU,CAAC,wGAAG,EAAE,OAAO;AACtD;AACO,MAAM,WAAW,CAAC;IACvB,OAAO,IAAA,yGAAI,EAAC,YAAY,wGAAG,KAAK,MAAM,KAAK,UAAU,CAAC,KAAK,wGAAG,IAAI;AACpE;AAEA,qDAAqD;AACrD,MAAM,cAAc,QAAQ,GAAG;AAC/B,MAAM,cAAc,IAAI;AACxB,QAAQ,GAAG,GAAG,IAAI,MAAM,aAAa;IACnC,KAAI,MAAM,EAAE,IAAI;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,+CAA+C;YAC/C,8CAA8C;YAC9C,oDAAoD;YACpD,yBAAyB;YACzB,YAAY,GAAG,CAAC;QAClB;QACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;IAC7B;IACA,KAAI,MAAM,EAAE,IAAI,EAAE,KAAK;QACrB,OAAO,QAAQ,GAAG,CAAC,QAAQ,MAAM;IACnC;AACF;AAEO,SAAS;IACd,OAAO,MAAM,IAAI,CAAC;AACpB"}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/transforms/postcss.ts"], "sourcesContent": ["declare const __turbopack_external_require__: (\n  id: string,\n  thunk: () => any,\n  esm?: boolean\n) => any\n\nimport type { Processor } from 'postcss'\n\n// @ts-ignore\nimport postcss from '@vercel/turbopack/postcss'\n// @ts-ignore\nimport importedConfig from 'CONFIG'\nimport { getReadEnvVariables, toPath, type TransformIpc } from './transforms'\n\nlet processor: Processor | undefined\n\nexport const init = async (ipc: TransformIpc) => {\n  let config = importedConfig\n  if (typeof config === 'function') {\n    config = await config({ env: 'development' })\n  }\n  if (typeof config === 'undefined') {\n    throw new Error(\n      'PostCSS config is undefined (make sure to export an function or object from config file)'\n    )\n  }\n  let plugins: any[]\n  if (Array.isArray(config.plugins)) {\n    plugins = config.plugins.map((plugin: [string, any] | string | any) => {\n      if (Array.isArray(plugin)) {\n        return plugin\n      } else if (typeof plugin === 'string') {\n        return [plugin, {}]\n      } else {\n        return plugin\n      }\n    })\n  } else if (typeof config.plugins === 'object') {\n    plugins = Object.entries(config.plugins).filter(([, options]) => options)\n  } else {\n    plugins = []\n  }\n  const loadedPlugins = plugins.map((plugin) => {\n    if (Array.isArray(plugin)) {\n      const [arg, options] = plugin\n      let pluginFactory = arg\n\n      if (typeof pluginFactory === 'string') {\n        pluginFactory = require(/* turbopackIgnore: true */ pluginFactory)\n      }\n\n      if (pluginFactory.default) {\n        pluginFactory = pluginFactory.default\n      }\n\n      return pluginFactory(options)\n    }\n    return plugin\n  })\n\n  processor = postcss(loadedPlugins)\n}\n\nexport default async function transform(\n  ipc: TransformIpc,\n  cssContent: string,\n  name: string,\n  sourceMap: boolean\n) {\n  const { css, map, messages } = await processor!.process(cssContent, {\n    from: name,\n    to: name,\n    map: sourceMap\n      ? {\n          inline: false,\n          annotation: false,\n        }\n      : undefined,\n  })\n\n  const assets = []\n  const filePaths: string[] = []\n  const buildFilePaths: string[] = []\n  const directories: Array<[string, string]> = []\n\n  for (const msg of messages) {\n    switch (msg.type) {\n      case 'asset':\n        assets.push({\n          file: msg.file,\n          content: msg.content,\n          sourceMap: !sourceMap\n            ? undefined\n            : typeof msg.sourceMap === 'string'\n              ? msg.sourceMap\n              : JSON.stringify(msg.sourceMap),\n          // There is also an info field, which we currently ignore\n        })\n        break\n      case 'dependency':\n      case 'missing-dependency':\n        filePaths.push(toPath(msg.file))\n        break\n      case 'build-dependency':\n        buildFilePaths.push(toPath(msg.file))\n        break\n      case 'dir-dependency':\n        directories.push([toPath(msg.dir), msg.glob])\n        break\n      case 'context-dependency':\n        directories.push([toPath(msg.dir), '**'])\n        break\n      default:\n        // TODO: do we need to do anything here?\n        break\n    }\n  }\n  ipc.sendInfo({\n    type: 'dependencies',\n    filePaths,\n    directories,\n    buildFilePaths,\n    envVariables: getReadEnvVariables(),\n  })\n  return {\n    css,\n    map: sourceMap ? JSON.stringify(map) : undefined,\n    assets,\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAQA,aAAa;AACb;AACA,aAAa;AACb;AACA;;;;AAEA,IAAI;AAEG,MAAM,OAAO,OAAO;IACzB,IAAI,SAAS,0HAAc;IAC3B,IAAI,OAAO,WAAW,YAAY;QAChC,SAAS,MAAM,OAAO;YAAE,KAAK;QAAc;IAC7C;IACA,IAAI,OAAO,WAAW,aAAa;QACjC,MAAM,IAAI,MACR;IAEJ;IACA,IAAI;IACJ,IAAI,MAAM,OAAO,CAAC,OAAO,OAAO,GAAG;QACjC,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,MAAM,OAAO,CAAC,SAAS;gBACzB,OAAO;YACT,OAAO,IAAI,OAAO,WAAW,UAAU;gBACrC,OAAO;oBAAC;oBAAQ,CAAC;iBAAE;YACrB,OAAO;gBACL,OAAO;YACT;QACF;IACF,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;QAC7C,UAAU,OAAO,OAAO,CAAC,OAAO,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,QAAQ,GAAK;IACnE,OAAO;QACL,UAAU,EAAE;IACd;IACA,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAC;QACjC,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,MAAM,CAAC,KAAK,QAAQ,GAAG;YACvB,IAAI,gBAAgB;YAEpB,IAAI,OAAO,kBAAkB,UAAU;gBACrC,gBAAgB,QAAQ,yBAAyB,GAAG;YACtD;YAEA,IAAI,cAAc,OAAO,EAAE;gBACzB,gBAAgB,cAAc,OAAO;YACvC;YAEA,OAAO,cAAc;QACvB;QACA,OAAO;IACT;IAEA,YAAY,IAAA,kJAAO,EAAC;AACtB;AAEe,eAAe,UAC5B,GAAiB,EACjB,UAAkB,EAClB,IAAY,EACZ,SAAkB;IAElB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,UAAW,OAAO,CAAC,YAAY;QAClE,MAAM;QACN,IAAI;QACJ,KAAK,YACD;YACE,QAAQ;YACR,YAAY;QACd,IACA;IACN;IAEA,MAAM,SAAS,EAAE;IACjB,MAAM,YAAsB,EAAE;IAC9B,MAAM,iBAA2B,EAAE;IACnC,MAAM,cAAuC,EAAE;IAE/C,KAAK,MAAM,OAAO,SAAU;QAC1B,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,OAAO,IAAI,CAAC;oBACV,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;oBACpB,WAAW,CAAC,YACR,YACA,OAAO,IAAI,SAAS,KAAK,WACvB,IAAI,SAAS,GACb,KAAK,SAAS,CAAC,IAAI,SAAS;gBAEpC;gBACA;YACF,KAAK;YACL,KAAK;gBACH,UAAU,IAAI,CAAC,IAAA,yIAAM,EAAC,IAAI,IAAI;gBAC9B;YACF,KAAK;gBACH,eAAe,IAAI,CAAC,IAAA,yIAAM,EAAC,IAAI,IAAI;gBACnC;YACF,KAAK;gBACH,YAAY,IAAI,CAAC;oBAAC,IAAA,yIAAM,EAAC,IAAI,GAAG;oBAAG,IAAI,IAAI;iBAAC;gBAC5C;YACF,KAAK;gBACH,YAAY,IAAI,CAAC;oBAAC,IAAA,yIAAM,EAAC,IAAI,GAAG;oBAAG;iBAAK;gBACxC;YACF;gBAEE;QACJ;IACF;IACA,IAAI,QAAQ,CAAC;QACX,MAAM;QACN;QACA;QACA;QACA,cAAc,IAAA,sJAAmB;IACnC;IACA,OAAO;QACL;QACA,KAAK,YAAY,KAAK,SAAS,CAAC,OAAO;QACvC;IACF;AACF"}}]}