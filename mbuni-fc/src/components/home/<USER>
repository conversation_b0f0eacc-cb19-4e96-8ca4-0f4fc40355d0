'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Star, Target, Shield, Zap, ArrowRight } from 'lucide-react'
import Container from '@/components/ui/Container'
import Card from '@/components/ui/Card'
import <PERSON><PERSON> from '@/components/ui/Button'

const PlayerHighlights: React.FC = () => {
  // Mock player data - in a real app, this would come from an API
  const featuredPlayers = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Forward",
      number: 10,
      image: "/images/players/marcus-rodriguez.jpg",
      stats: {
        goals: 18,
        assists: 12,
        matches: 25
      },
      achievements: ["Top Scorer", "Player of the Month"],
      description: "Clinical finisher with exceptional pace and positioning.",
      icon: Target
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Midfielder",
      number: 8,
      image: "/images/players/david-thompson.jpg",
      stats: {
        goals: 6,
        assists: 15,
        matches: 28
      },
      achievements: ["Most Assists", "Captain"],
      description: "Creative playmaker who controls the tempo of the game.",
      icon: <PERSON>
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Defender",
      number: 4,
      image: "/images/players/alex-johnson.jpg",
      stats: {
        goals: 3,
        assists: 4,
        matches: 30
      },
      achievements: ["Most Clean Sheets", "Defensive Rock"],
      description: "Solid defender with excellent aerial ability and leadership.",
      icon: Shield
    },
    {
      id: 4,
      name: "Ryan Mitchell",
      position: "Winger",
      number: 7,
      image: "/images/players/ryan-mitchell.jpg",
      stats: {
        goals: 12,
        assists: 8,
        matches: 24
      },
      achievements: ["Fastest Player", "Breakthrough Player"],
      description: "Explosive winger with incredible dribbling skills.",
      icon: Zap
    }
  ]

  return (
    <section className="section-padding bg-secondary-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-pattern opacity-5" />
      
      <Container className="relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="heading-2 text-white mb-4">Player Highlights</h2>
          <p className="body-large text-secondary-300 max-w-2xl mx-auto">
            Meet our star players who are making the difference on the pitch. 
            Their skill, dedication, and passion drive our success.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {featuredPlayers.map((player, index) => {
            const Icon = player.icon
            return (
              <motion.div
                key={player.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="bg-secondary-800 border-secondary-700 hover:bg-secondary-750 transition-all duration-300 group">
                  <div className="relative">
                    {/* Player Image Placeholder */}
                    <div className="relative h-48 bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg mb-4 overflow-hidden">
                      <div className="absolute inset-0 bg-black/20" />
                      <div className="absolute bottom-4 left-4">
                        <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                          <span className="text-primary-600 font-bold text-sm">
                            {player.number}
                          </span>
                        </div>
                      </div>
                      <div className="absolute top-4 right-4">
                        <Icon className="w-6 h-6 text-white/80" />
                      </div>
                    </div>

                    {/* Player Info */}
                    <div className="space-y-3">
                      <div>
                        <h3 className="text-lg font-bold text-white group-hover:text-primary-300 transition-colors">
                          {player.name}
                        </h3>
                        <p className="text-primary-400 text-sm font-medium">
                          {player.position}
                        </p>
                      </div>

                      <p className="text-secondary-300 text-sm leading-relaxed">
                        {player.description}
                      </p>

                      {/* Stats */}
                      <div className="grid grid-cols-3 gap-2 py-3 border-t border-secondary-700">
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">
                            {player.stats.goals}
                          </div>
                          <div className="text-xs text-secondary-400">Goals</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">
                            {player.stats.assists}
                          </div>
                          <div className="text-xs text-secondary-400">Assists</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-white">
                            {player.stats.matches}
                          </div>
                          <div className="text-xs text-secondary-400">Matches</div>
                        </div>
                      </div>

                      {/* Achievements */}
                      <div className="space-y-1">
                        {player.achievements.slice(0, 2).map((achievement, idx) => (
                          <div
                            key={idx}
                            className="text-xs bg-primary-600/20 text-primary-300 px-2 py-1 rounded-full inline-block mr-1"
                          >
                            {achievement}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Team Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="bg-secondary-800 rounded-2xl p-8 mb-8"
        >
          <h3 className="text-2xl font-bold text-white mb-6 text-center">
            Season Statistics
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-400 mb-2">68</div>
              <div className="text-secondary-300 text-sm">Goals Scored</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-400 mb-2">23</div>
              <div className="text-secondary-300 text-sm">Clean Sheets</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-400 mb-2">78%</div>
              <div className="text-secondary-300 text-sm">Win Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-400 mb-2">2nd</div>
              <div className="text-secondary-300 text-sm">League Position</div>
            </div>
          </div>
        </motion.div>

        {/* View Full Squad Button */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <Button 
            variant="outline" 
            size="lg"
            className="border-white text-white hover:bg-white hover:text-secondary-900"
            icon={<ArrowRight className="w-5 h-5" />}
            iconPosition="right"
          >
            View Full Squad
          </Button>
        </motion.div>
      </Container>
    </section>
  )
}

export default PlayerHighlights
